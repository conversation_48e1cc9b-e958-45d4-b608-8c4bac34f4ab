@echo off
REM Gita Life Helper - Quick Start Script for Windows
REM This script sets up the environment and runs the application

echo 🕉️  Gita Life Helper - Quick Start
echo ==================================

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat

REM Install dependencies
echo 📥 Installing dependencies...
pip install -r requirements.txt

REM Check if embeddings exist
if not exist "embeddings\chroma.sqlite3" (
    echo 🧠 Setting up embeddings (this may take a few minutes)...
    python scripts\embed_verses.py
) else (
    echo ✅ Embeddings already exist
)

REM Ask user which interface to run
echo.
echo 🚀 Choose how to run the application:
echo 1) Web Interface (Gradio) - Recommended for most users
echo 2) API Server (FastAPI) - For developers and integrations
echo 3) Setup only (don't run anything)

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo 🌐 Starting Gradio web interface...
    echo 📱 Open http://localhost:7860 in your browser
    python interface\app_gradio.py
) else if "%choice%"=="2" (
    echo 🔧 Starting FastAPI server...
    echo 📖 API docs available at http://localhost:8000/docs
    python interface\app_api.py
) else if "%choice%"=="3" (
    echo ✅ Setup complete! You can now run:
    echo    python interface\app_gradio.py  (for web interface)
    echo    python interface\app_api.py     (for API server)
) else (
    echo ❌ Invalid choice. Please run the script again.
)

pause
