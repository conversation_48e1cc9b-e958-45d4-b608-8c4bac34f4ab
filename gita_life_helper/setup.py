#!/usr/bin/env python3
"""
Setup script for Gita Life Helper
This script helps users set up the project quickly.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install required dependencies"""
    return run_command("pip install -r requirements.txt", "Installing dependencies")

def setup_embeddings():
    """Set up embeddings and database"""
    return run_command("python scripts/embed_verses.py", "Setting up embeddings and database")

def create_env_file():
    """Create .env file from template if it doesn't exist"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("\n📝 Creating .env file from template...")
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✅ .env file created")
        print("💡 Edit .env file to add your API keys for AI explanations")
        return True
    elif env_file.exists():
        print("✅ .env file already exists")
        return True
    else:
        print("⚠️  No .env.example file found")
        return False

def test_installation():
    """Test if the installation works"""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        sys.path.append(str(Path.cwd()))
        from scripts.search import GitaSearcher
        from scripts.explain import GitaExplainer
        
        # Test searcher
        searcher = GitaSearcher()
        results = searcher.search_verses("test", n_results=1)
        
        if results:
            print("✅ Search functionality working")
            print(f"✅ Database contains {searcher.collection.count()} verses")
            return True
        else:
            print("❌ Search returned no results")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def main():
    """Main setup function"""
    print("🕉️  Welcome to Gita Life Helper Setup")
    print("=" * 50)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Failed to install dependencies. Please check your Python environment.")
        sys.exit(1)
    
    # Create .env file
    create_env_file()
    
    # Setup embeddings
    if not setup_embeddings():
        print("\n❌ Failed to set up embeddings. Please check the error messages above.")
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("\n❌ Installation test failed. Please check the error messages above.")
        sys.exit(1)
    
    # Success message
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. (Optional) Edit .env file to add API keys for AI explanations")
    print("2. Run the web interface: python interface/app_gradio.py")
    print("3. Or run the API server: python interface/app_api.py")
    print("\n💡 Visit http://localhost:7860 for the web interface")
    print("💡 Visit http://localhost:8000/docs for API documentation")
    print("\n🙏 May this tool bring you wisdom and peace!")

if __name__ == "__main__":
    main()
