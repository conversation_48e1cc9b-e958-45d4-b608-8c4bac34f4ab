# 🕉️ Gita Life Helper

A semantic search-powered assistant that provides life guidance from the Bhagavad Gita based on your personal questions and challenges.

## ✨ Features

- **Semantic Search**: Find relevant verses using natural language queries
- **AI-Powered Explanations**: Get contextual interpretations using Mistral AI or OpenAI
- **Web Interface**: Easy-to-use Gradio interface for interactive guidance
- **REST API**: FastAPI backend for integration with other applications
- **Chapter Explorer**: Browse verses by specific chapters
- **Multilingual Support**: Includes Sanskrit, Hindi, and English translations

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**For Linux/Mac:**
```bash
cd gita_life_helper
./run.sh
```

**For Windows:**
```bash
cd gita_life_helper
run.bat
```

The script will:
- Create a virtual environment
- Install all dependencies
- Set up the database and embeddings
- Let you choose which interface to run

### Option 2: Manual Setup

```bash
# 1. Create virtual environment
python -m venv venv

# 2. Activate virtual environment
# On Linux/Mac:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Setup embeddings
python scripts/embed_verses.py

# 5. Run the application
python interface/app_gradio.py  # Web interface
# OR
python interface/app_api.py     # API server
```

### Access the Application

- **Web Interface**: http://localhost:7860
- **API Documentation**: http://localhost:8000/docs

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the project root for AI features:

```bash
# For Mistral AI (recommended - cost-effective)
MISTRAL_API_KEY=your_mistral_api_key_here

# OR for OpenAI
OPENAI_API_KEY=your_openai_api_key_here
```

**Note**: AI explanations are optional. The system works with simple explanations if no API keys are provided.

### Hardware Requirements

- **Minimum**: 4GB RAM, any modern CPU
- **Recommended**: 8GB+ RAM, GPU support for faster embeddings
- **Storage**: ~500MB for embeddings and data

## 📖 Usage Examples

### Web Interface

1. **Life Guidance**: Ask questions like:
   - "I'm feeling anxious about my career decisions"
   - "How can I find inner peace?"
   - "What should I do when facing difficult relationships?"

2. **Chapter Explorer**: Browse specific chapters or search within them

### API Usage

```python
import requests

# Get life guidance
response = requests.post("http://localhost:8000/guidance", 
    json={"query": "I'm feeling lost in life", "num_verses": 3})

# Search verses
response = requests.get("http://localhost:8000/search?q=peace&num_verses=5")

# Get specific verse
response = requests.get("http://localhost:8000/verse/2/47")
```

## 🏗️ Project Structure

```
gita_life_helper/
├── data/
│   └── Bhagwad_Gita.csv          # Source Gita dataset
├── scripts/
│   ├── embed_verses.py           # Data processing and embedding
│   ├── search.py                 # Semantic search functionality
│   └── explain.py                # AI explanation generation
├── interface/
│   ├── app_gradio.py            # Gradio web interface
│   └── app_api.py               # FastAPI backend
├── embeddings/                   # ChromaDB storage (created automatically)
├── requirements.txt              # Python dependencies
└── README.md                     # This file
```

## 🔍 How It Works

1. **Data Processing**: The Bhagavad Gita CSV is cleaned and processed
2. **Embedding Creation**: Each verse is converted to a vector using SentenceTransformers
3. **Vector Storage**: Embeddings are stored in ChromaDB for fast similarity search
4. **Query Processing**: User queries are embedded and matched against verse embeddings
5. **AI Explanation**: Relevant verses are sent to AI models for contextual interpretation
6. **Response Generation**: Formatted guidance is returned to the user

## 🛠️ Development

### Adding New Features

1. **Custom Search Filters**: Modify `search.py` to add filtering by themes, characters, etc.
2. **Additional AI Providers**: Extend `explain.py` to support more AI services
3. **Enhanced UI**: Customize the Gradio interface in `app_gradio.py`
4. **Mobile App**: Use the FastAPI backend to build mobile applications

### Testing

```bash
# Test individual components
python scripts/search.py          # Test search functionality
python scripts/explain.py         # Test explanation generation
python scripts/embed_verses.py    # Re-run embedding process
```

## 📊 API Endpoints

- `GET /` - API information
- `POST /guidance` - Get life guidance
- `GET /search` - Search verses by query
- `GET /chapter/{chapter_num}` - Get verses from specific chapter
- `GET /verse/{chapter}/{verse}` - Get specific verse
- `GET /health` - Health check

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **Bhagavad Gita**: The timeless spiritual text that forms the foundation of this project
- **SentenceTransformers**: For providing excellent embedding models
- **ChromaDB**: For efficient vector storage and retrieval
- **Gradio**: For making it easy to create beautiful web interfaces
- **FastAPI**: For the robust API framework

## 💡 Example Queries

Try asking these types of questions:

- **Career & Purpose**: "I don't know what my life's purpose is"
- **Relationships**: "How to deal with difficult people at work"
- **Mental Health**: "I'm feeling overwhelmed and stressed"
- **Decision Making**: "How to make important life decisions"
- **Spiritual Growth**: "How can I grow spiritually"
- **Challenges**: "How to stay motivated during tough times"

## 🔮 Future Enhancements

- Voice input and output
- PDF export of guidance sessions
- Personalized recommendations based on user history
- Integration with meditation and mindfulness apps
- Mobile application
- Multi-language support for queries
- Advanced filtering and categorization

---

*May this tool help you find wisdom, peace, and guidance on your life's journey through the eternal teachings of the Bhagavad Gita.*
