#!/bin/bash

# Gita Life Helper - Quick Start Script
# This script sets up the environment and runs the application

echo "🕉️  Gita Life Helper - Quick Start"
echo "=================================="

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python -m venv venv
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Check if embeddings exist
if [ ! -d "embeddings/chroma.sqlite3" ] && [ ! -f "embeddings/chroma.sqlite3" ]; then
    echo "🧠 Setting up embeddings (this may take a few minutes)..."
    python scripts/embed_verses.py
else
    echo "✅ Embeddings already exist"
fi

# Ask user which interface to run
echo ""
echo "🚀 Choose how to run the application:"
echo "1) Web Interface (Gradio) - Recommended for most users"
echo "2) API Server (FastAPI) - For developers and integrations"
echo "3) Setup only (don't run anything)"

read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo "🌐 Starting Gradio web interface..."
        echo "📱 Open http://localhost:7860 in your browser"
        python interface/app_gradio.py
        ;;
    2)
        echo "🔧 Starting FastAPI server..."
        echo "📖 API docs available at http://localhost:8000/docs"
        python interface/app_api.py
        ;;
    3)
        echo "✅ Setup complete! You can now run:"
        echo "   python interface/app_gradio.py  (for web interface)"
        echo "   python interface/app_api.py     (for API server)"
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        ;;
esac
