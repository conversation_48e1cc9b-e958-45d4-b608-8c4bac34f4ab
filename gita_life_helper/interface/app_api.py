"""
FastAPI Backend for Gita Life Helper
This provides a REST API for the Gita Life Helper functionality.
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import sys
import os
from pathlib import Path
import logging

# Add parent directory to path to import our modules
sys.path.append(str(Path(__file__).parent.parent))

from scripts.search import GitaSearcher
from scripts.explain import GitaExplainer

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Gita Life Helper API",
    description="Get wisdom and guidance from the Bhagavad Gita for life's challenges",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class QueryRequest(BaseModel):
    query: str
    num_verses: Optional[int] = 3

class VerseResponse(BaseModel):
    id: str
    content: str
    chapter: int
    verse: int
    similarity_score: float
    shloka: Optional[str] = None
    hindi_meaning: Optional[str] = None

class GuidanceResponse(BaseModel):
    query: str
    verses: List[VerseResponse]
    explanation: str
    success: bool
    message: Optional[str] = None

class ChapterRequest(BaseModel):
    chapter: int
    query: Optional[str] = None
    num_verses: Optional[int] = 10

# Global variables for components
searcher = None
explainer = None

@app.on_event("startup")
async def startup_event():
    """Initialize components on startup"""
    global searcher, explainer
    
    try:
        # Change to project root directory
        project_root = Path(__file__).parent.parent
        os.chdir(project_root)
        
        # Initialize searcher
        searcher = GitaSearcher()
        logger.info("Searcher initialized successfully")
        
        # Try to initialize AI explainer
        try:
            if os.getenv("MISTRAL_API_KEY"):
                explainer = GitaExplainer("mistral")
                logger.info("Mistral AI explainer initialized")
            elif os.getenv("OPENAI_API_KEY"):
                explainer = GitaExplainer("openai")
                logger.info("OpenAI explainer initialized")
            else:
                logger.warning("No AI API keys found. Simple explanations will be used.")
        except Exception as e:
            logger.warning(f"Could not initialize AI explainer: {str(e)}")
        
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        raise

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to Gita Life Helper API",
        "version": "1.0.0",
        "endpoints": {
            "guidance": "/guidance - Get life guidance from Gita verses",
            "search": "/search - Search for verses by query",
            "chapter": "/chapter/{chapter_num} - Get verses from specific chapter",
            "verse": "/verse/{chapter}/{verse} - Get specific verse",
            "docs": "/docs - API documentation"
        }
    }

@app.post("/guidance", response_model=GuidanceResponse)
async def get_guidance(request: QueryRequest):
    """Get life guidance based on user query"""
    try:
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="Query cannot be empty")
        
        # Search for relevant verses
        verses = searcher.search_verses(request.query, n_results=request.num_verses)
        
        if not verses:
            return GuidanceResponse(
                query=request.query,
                verses=[],
                explanation="No relevant verses found for your query. Please try rephrasing your question.",
                success=False,
                message="No verses found"
            )
        
        # Convert to response format
        verse_responses = []
        for verse in verses:
            verse_response = VerseResponse(
                id=verse['id'],
                content=verse['content'],
                chapter=verse['chapter'],
                verse=verse['verse'],
                similarity_score=verse['similarity_score']
            )
            if 'shloka' in verse:
                verse_response.shloka = verse['shloka']
            if 'hindi_meaning' in verse:
                verse_response.hindi_meaning = verse['hindi_meaning']
            verse_responses.append(verse_response)
        
        # Get explanation
        explanation = ""
        if explainer:
            try:
                explanation = explainer.explain_verses(request.query, verses)
            except Exception as e:
                logger.warning(f"AI explanation failed: {str(e)}")
                explanation = create_simple_explanation(verses)
        else:
            explanation = create_simple_explanation(verses)
        
        return GuidanceResponse(
            query=request.query,
            verses=verse_responses,
            explanation=explanation,
            success=True
        )
        
    except Exception as e:
        logger.error(f"Error in get_guidance: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/search")
async def search_verses(
    q: str = Query(..., description="Search query"),
    num_verses: int = Query(5, ge=1, le=10, description="Number of verses to return")
):
    """Search for verses by query"""
    try:
        verses = searcher.search_verses(q, n_results=num_verses)
        
        verse_responses = []
        for verse in verses:
            verse_response = VerseResponse(
                id=verse['id'],
                content=verse['content'],
                chapter=verse['chapter'],
                verse=verse['verse'],
                similarity_score=verse['similarity_score']
            )
            if 'shloka' in verse:
                verse_response.shloka = verse['shloka']
            if 'hindi_meaning' in verse:
                verse_response.hindi_meaning = verse['hindi_meaning']
            verse_responses.append(verse_response)
        
        return {
            "query": q,
            "verses": verse_responses,
            "total_found": len(verse_responses)
        }
        
    except Exception as e:
        logger.error(f"Error in search_verses: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/chapter/{chapter_num}")
async def get_chapter_verses(
    chapter_num: int,
    query: Optional[str] = Query(None, description="Optional search query within chapter"),
    num_verses: int = Query(10, ge=1, le=50, description="Number of verses to return")
):
    """Get verses from a specific chapter"""
    try:
        if chapter_num < 1 or chapter_num > 18:
            raise HTTPException(status_code=400, detail="Chapter number must be between 1 and 18")
        
        verses = searcher.search_by_chapter(chapter_num, query, n_results=num_verses)
        
        verse_responses = []
        for verse in verses:
            verse_response = VerseResponse(
                id=verse['id'],
                content=verse['content'],
                chapter=verse['chapter'],
                verse=verse['verse'],
                similarity_score=verse['similarity_score']
            )
            if 'shloka' in verse:
                verse_response.shloka = verse['shloka']
            if 'hindi_meaning' in verse:
                verse_response.hindi_meaning = verse['hindi_meaning']
            verse_responses.append(verse_response)
        
        return {
            "chapter": chapter_num,
            "query": query,
            "verses": verse_responses,
            "total_found": len(verse_responses)
        }
        
    except Exception as e:
        logger.error(f"Error in get_chapter_verses: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/verse/{chapter}/{verse_num}")
async def get_specific_verse(chapter: int, verse_num: int):
    """Get a specific verse by chapter and verse number"""
    try:
        if chapter < 1 or chapter > 18:
            raise HTTPException(status_code=400, detail="Chapter number must be between 1 and 18")
        
        verse = searcher.get_verse_by_reference(chapter, verse_num)
        
        if not verse:
            raise HTTPException(status_code=404, detail=f"Verse {chapter}.{verse_num} not found")
        
        verse_response = VerseResponse(
            id=verse['id'],
            content=verse['content'],
            chapter=verse['chapter'],
            verse=verse['verse'],
            similarity_score=1.0  # Exact match
        )
        if 'shloka' in verse:
            verse_response.shloka = verse['shloka']
        if 'hindi_meaning' in verse:
            verse_response.hindi_meaning = verse['hindi_meaning']
        
        return verse_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_specific_verse: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check if searcher is working
        test_results = searcher.search_verses("test", n_results=1)
        
        return {
            "status": "healthy",
            "searcher": "working",
            "explainer": "working" if explainer else "simple_mode",
            "database_verses": searcher.collection.count() if searcher else 0
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

def create_simple_explanation(verses):
    """Create simple explanation without AI"""
    explanation = "These verses from the Bhagavad Gita offer timeless wisdom:\n\n"
    
    for i, verse in enumerate(verses, 1):
        explanation += f"{i}. Chapter {verse['chapter']}, Verse {verse['verse']} teaches us about "
        
        content_lower = verse['content'].lower()
        if any(word in content_lower for word in ['duty', 'action', 'work']):
            explanation += "performing our duties without attachment to results. "
        elif any(word in content_lower for word in ['mind', 'peace', 'calm']):
            explanation += "maintaining mental peace and equanimity. "
        elif any(word in content_lower for word in ['knowledge', 'wisdom']):
            explanation += "the importance of wisdom and understanding. "
        else:
            explanation += "spiritual principles for righteous living. "
        
        explanation += "\n"
    
    explanation += "\nThese teachings remind us to focus on our actions while remaining detached from outcomes, leading to inner peace and spiritual growth."
    
    return explanation

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
