"""
Gradio Web Interface for Gita Life Helper
This provides an easy-to-use web interface for getting life advice from the Bhagavad Gita.
"""

import gradio as gr
import sys
import os
from pathlib import Path

# Add parent directory to path to import our modules
sys.path.append(str(Path(__file__).parent.parent))

from scripts.search import GitaSearcher
from scripts.explain import GitaExplainer
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GitaLifeHelper:
    def __init__(self):
        """Initialize the Gita Life Helper"""
        try:
            # Change to project root directory
            project_root = Path(__file__).parent.parent
            os.chdir(project_root)
            
            # Initialize components
            self.searcher = GitaSearcher()
            
            # Try to initialize AI explainer, fallback to simple explanations if API keys not available
            self.ai_explainer = None
            try:
                # Try Mistral first, then OpenAI
                if os.getenv("MISTRAL_API_KEY"):
                    self.ai_explainer = GitaExplainer("mistral")
                    logger.info("Using Mistral AI for explanations")
                elif os.getenv("OPENAI_API_KEY"):
                    self.ai_explainer = GitaExplainer("openai")
                    logger.info("Using OpenAI for explanations")
                else:
                    logger.warning("No AI API keys found. Using simple explanations.")
            except Exception as e:
                logger.warning(f"Could not initialize AI explainer: {str(e)}. Using simple explanations.")
            
            logger.info("Gita Life Helper initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Gita Life Helper: {str(e)}")
            raise
    
    def get_advice(self, user_query: str, num_verses: int = 3) -> str:
        """
        Get life advice based on user query
        
        Args:
            user_query: User's life problem or question
            num_verses: Number of verses to retrieve
            
        Returns:
            Formatted advice with verses and explanation
        """
        if not user_query.strip():
            return "Please enter your question or describe your situation."
        
        try:
            # Search for relevant verses
            logger.info(f"Processing query: {user_query}")
            verses = self.searcher.search_verses(user_query, n_results=num_verses)
            
            if not verses:
                return "I couldn't find relevant verses for your query. Please try rephrasing your question."
            
            # Format response
            response = f"## 🙏 Guidance from the Bhagavad Gita\n\n"
            response += f"**Your Question:** {user_query}\n\n"
            
            # Add verses
            response += "### 📖 Relevant Verses:\n\n"
            for i, verse in enumerate(verses, 1):
                response += f"**{i}. Chapter {verse['chapter']}, Verse {verse['verse']}** "
                response += f"(Relevance: {verse['similarity_score']:.1%})\n\n"
                response += f"*\"{verse['content']}\"*\n\n"
            
            # Add AI explanation if available
            if self.ai_explainer:
                try:
                    explanation = self.ai_explainer.explain_verses(user_query, verses)
                    response += "### 💭 Interpretation & Guidance:\n\n"
                    response += explanation + "\n\n"
                except Exception as e:
                    logger.warning(f"AI explanation failed: {str(e)}")
                    # Fallback to simple explanation
                    simple_explanation = self.create_simple_guidance(verses)
                    response += "### 💭 Guidance:\n\n"
                    response += simple_explanation + "\n\n"
            else:
                # Use simple explanation
                simple_explanation = self.create_simple_guidance(verses)
                response += "### 💭 Guidance:\n\n"
                response += simple_explanation + "\n\n"
            
            response += "---\n*May these ancient teachings bring you peace and clarity on your journey.*"
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return f"Sorry, I encountered an error while processing your request: {str(e)}"
    
    def create_simple_guidance(self, verses):
        """Create simple guidance without AI"""
        guidance = "These verses from the Bhagavad Gita offer timeless wisdom for your situation:\n\n"
        
        # Add some general interpretations based on common themes
        themes = []
        for verse in verses:
            content_lower = verse['content'].lower()
            if any(word in content_lower for word in ['duty', 'action', 'work']):
                themes.append("duty and action")
            if any(word in content_lower for word in ['mind', 'peace', 'calm']):
                themes.append("mental peace")
            if any(word in content_lower for word in ['attachment', 'desire', 'result']):
                themes.append("detachment from results")
            if any(word in content_lower for word in ['knowledge', 'wisdom', 'understand']):
                themes.append("wisdom and understanding")
        
        if "duty and action" in themes:
            guidance += "• **Focus on your actions, not the results.** Do your best in whatever role you have, but don't be anxious about outcomes.\n\n"
        
        if "mental peace" in themes:
            guidance += "• **Cultivate inner peace.** Regular meditation, mindfulness, and spiritual practice can help calm your mind.\n\n"
        
        if "detachment from results" in themes:
            guidance += "• **Practice detachment.** Work sincerely but release attachment to specific outcomes. This reduces anxiety and stress.\n\n"
        
        if "wisdom and understanding" in themes:
            guidance += "• **Seek wisdom.** Study spiritual texts, reflect on life's deeper meanings, and learn from wise teachers.\n\n"
        
        guidance += "Remember, the Gita teaches us that challenges are opportunities for growth. Stay steady in your practice and trust the process."
        
        return guidance
    
    def search_by_chapter_interface(self, chapter_num: int, query: str = "") -> str:
        """Interface for searching within a specific chapter"""
        try:
            if chapter_num < 1 or chapter_num > 18:
                return "Please enter a chapter number between 1 and 18."
            
            verses = self.searcher.search_by_chapter(chapter_num, query if query.strip() else None, n_results=5)
            
            if not verses:
                return f"No verses found in Chapter {chapter_num}."
            
            response = f"## Chapter {chapter_num} - Bhagavad Gita\n\n"
            if query.strip():
                response += f"**Search query:** {query}\n\n"
            
            for verse in verses:
                response += f"**Verse {verse['verse']}**\n\n"
                response += f"*\"{verse['content']}\"*\n\n"
                if query.strip():
                    response += f"Relevance: {verse['similarity_score']:.1%}\n\n"
                response += "---\n\n"
            
            return response
            
        except Exception as e:
            return f"Error searching chapter: {str(e)}"

def create_interface():
    """Create and configure the Gradio interface"""
    
    # Initialize the helper
    helper = GitaLifeHelper()
    
    # Create the interface
    with gr.Blocks(title="Gita Life Helper", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🕉️ Gita Life Helper
        
        **Get wisdom and guidance from the Bhagavad Gita for your life's challenges**
        
        Ask any question about your life, career, relationships, or spiritual journey, and receive relevant verses 
        from the Bhagavad Gita along with practical interpretations.
        """)
        
        with gr.Tab("💭 Life Guidance"):
            with gr.Row():
                with gr.Column(scale=2):
                    query_input = gr.Textbox(
                        label="What's on your mind?",
                        placeholder="e.g., I'm feeling anxious about my career decisions...",
                        lines=3
                    )
                    num_verses = gr.Slider(
                        minimum=1,
                        maximum=5,
                        value=3,
                        step=1,
                        label="Number of verses to show"
                    )
                    submit_btn = gr.Button("Get Guidance", variant="primary")
                
                with gr.Column(scale=3):
                    output = gr.Markdown(label="Guidance")
            
            # Example queries
            gr.Markdown("### 💡 Example Questions:")
            example_queries = [
                "I'm feeling lost and don't know my life's purpose",
                "How can I deal with anxiety and stress?",
                "I'm struggling with difficult relationships",
                "How to find motivation when feeling discouraged?",
                "What should I do when facing tough decisions?"
            ]
            
            for example in example_queries:
                gr.Button(example, size="sm").click(
                    lambda x=example: x, outputs=query_input
                )
        
        with gr.Tab("📖 Chapter Explorer"):
            with gr.Row():
                with gr.Column():
                    chapter_input = gr.Number(
                        label="Chapter Number (1-18)",
                        value=1,
                        minimum=1,
                        maximum=18
                    )
                    chapter_query = gr.Textbox(
                        label="Optional: Search within chapter",
                        placeholder="e.g., duty, peace, wisdom..."
                    )
                    chapter_btn = gr.Button("Explore Chapter", variant="primary")
                
                with gr.Column(scale=2):
                    chapter_output = gr.Markdown(label="Chapter Verses")
        
        # Event handlers
        submit_btn.click(
            helper.get_advice,
            inputs=[query_input, num_verses],
            outputs=output
        )
        
        chapter_btn.click(
            helper.search_by_chapter_interface,
            inputs=[chapter_input, chapter_query],
            outputs=chapter_output
        )
        
        # Footer
        gr.Markdown("""
        ---
        *Built with ❤️ to share the timeless wisdom of the Bhagavad Gita*
        
        **Note:** This tool provides spiritual guidance based on ancient texts. For serious personal issues, 
        please also consult with qualified professionals.
        """)
    
    return app

def main():
    """Main function to run the Gradio app"""
    try:
        app = create_interface()
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            show_error=True
        )
    except Exception as e:
        logger.error(f"Error launching app: {str(e)}")
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
