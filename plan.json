{"project_name": "Gita-Based Life Decision Helper", "description": "A semantic search-powered assistant that gives advice from the Bhagavad Gita based on user-input life problems.", "tech_stack": {"language": "Python", "libraries": ["pandas", "sentence-transformers", "chromadb", "mistralai (or openai)", "langchain", "fastapi (or gradio)", "u<PERSON><PERSON>"]}, "hardware_requirements": {"minimum_gpu": "GTX 1650", "cpu": "Ryzen 5 or better", "ram": "8GB+ recommended"}, "data": {"input_file": "Bhagwad_Gita.csv", "columns_required": ["Chapter", "Verse", "Translation"], "preprocessing_steps": ["Rename 'Translation' to 'content'", "Create unique ID column", "Drop rows with missing content"]}, "folder_structure": {"root": "gita_life_helper", "subdirectories": ["data", "scripts", "embeddings", "interface"], "files": {"data/Bhagwad_Gita.csv": "Source Gita dataset", "scripts/embed_verses.py": "Embeds verses using SentenceTransformer", "scripts/search.py": "Handles semantic search", "scripts/explain.py": "Calls Mistral or GPT API for verse explanation", "interface/app_gradio.py": "Gradio-based UI", "interface/app_api.py": "FastAPI-based backend (optional)", "requirements.txt": "All required libraries"}}, "pipeline": [{"step": "Data Load", "script": "embed_verses.py", "description": "Load and clean the CSV file."}, {"step": "Embedding", "script": "embed_verses.py", "description": "Use SentenceTransformer to embed each verse."}, {"step": "Vector DB Setup", "script": "embed_verses.py", "description": "Store verse vectors in ChromaDB for fast retrieval."}, {"step": "Query Handling", "script": "search.py", "description": "Take user query, embed it, and retrieve top matching verses."}, {"step": "Explanation Generation", "script": "explain.py", "description": "Send selected verse + user query to Mistral/GPT for plain-language explanation."}, {"step": "Frontend Interface", "script": "app_gradio.py", "description": "Gradio app for easy web interface. Enter query and see responses."}], "example_usage": {"input": "I am feeling lost and confused about my career.", "output": ["📖 Chapter 2, Verse 47: 'You have the right to perform your duty but not the fruits of action...'", "💬 Interpretation: This verse reminds you to focus on action, not results. Do your work sincerely and let go of worry about outcomes."]}, "deployment_options": ["Run locally with: python interface/app_gradio.py", "Deploy FastAPI with: uvicorn interface.app_api:app --reload", "Package with Dock<PERSON> (optional)"], "optional_enhancements": ["Add Hindi/Sanskrit display toggle", "Allow exporting selected advice to PDF", "Add voice input and output", "Build a mobile version using React Native"]}